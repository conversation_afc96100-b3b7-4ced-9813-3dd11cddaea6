import 'package:dio/dio.dart';
import '../models/workflow/workflow_manual_response_model.dart';
import 'base_api_service.dart';
import 'auth_service.dart';
import '../utils/logger.dart';

/// Service for handling workflow validation API operations
class WorkflowValidationService extends BaseApiService {
  static const String _baseUrl = 'http://10.26.1.52:8100';
  static const String _validateEndpoint = '/api/workflows/validate';

  // Auth service for getting user data
  final AuthService _authService = AuthService();

  /// Validate text using the workflows validation API
  Future<WorkFlowModelResponseModel> validateText(String text) async {
    try {
      Logger.info('Validating workflow text: ${text.substring(0, text.length > 50 ? 50 : text.length)}...');

      // Prepare form data
      final formData = FormData.fromMap({
        'text': text,
      });

      // Get a valid token (optional for this endpoint, but good practice)
      final token = await _authService.getValidToken();
      
      final options = Options(
        headers: {
          'Content-Type': 'multipart/form-data',
          if (token != null) 'Authorization': 'Bearer $token',
        },
      );

      // Make the API call
      final fullUrl = '$_baseUrl$_validateEndpoint';
      
      final response = await dio.post(
        fullUrl,
        data: formData,
        options: options,
      );

      Logger.info('Workflow validation response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        Logger.info('Workflow validation successful');
        return WorkFlowModelResponseModel.fromJson(response.data);
      } else {
        final errorMessage = response.data['message'] ?? 'Failed to validate workflow text';
        Logger.error('Failed to validate workflow text: $errorMessage');
        throw Exception(errorMessage);
      }
    } catch (e) {
      Logger.error('Exception during workflow validation: $e');
      if (e is DioException) {
        if (e.response != null) {
          final errorMessage = e.response?.data['message'] ?? 'Workflow validation failed';
          throw Exception(errorMessage);
        } else {
          throw Exception('Network error during workflow validation');
        }
      }
      throw Exception('Failed to validate workflow text: $e');
    }
  }

  @override
  Future<String?> getValidToken() async {
    return await _authService.getValidToken();
  }

  @override
  Future<String?> getUserId() async {
    return await _authService.getUserId();
  }
}
