// To parse this JSON data, do
//
//     final agentData = agentDataFromJson(jsonString);

import 'dart:convert';

AgentData agentDataFromJson(String str) => AgentData.fromJson(json.decode(str));

String agentDataToJson(AgentData data) => json.encode(data.toJson());

class AgentData {
  List<AgentInfo>? agents;
  AgentSystemInfo?
      systemInfo; // Removed final to allow updating selectedVersion

  // Maps to track selected agent versions
  Map<String, String> selectedVersions = {};

  AgentData({
    this.agents = const [],
    this.systemInfo,
    Map<String, String>? selectedVersions,
  }) : selectedVersions = selectedVersions ?? {};

  AgentData copyWith({
    List<AgentInfo>? agents,
    AgentSystemInfo? systemInfo,
    Map<String, String>? selectedVersions,
  }) =>
      AgentData(
        agents: agents ?? this.agents,
        systemInfo: systemInfo ?? this.systemInfo,
        selectedVersions: selectedVersions ?? this.selectedVersions,
      );

  // Method to set selected version for an agent
  void setSelectedVersion(String agentId, String version) {
    selectedVersions[agentId] = version;

    // Also update the systemInfo's selectedVersion if it exists
    if (systemInfo != null) {
      systemInfo = systemInfo!.copyWith(selectedVersion: version);
    }
  }

  // Method to get selected version for an agent
  String getSelectedVersion(String agentId) {
    // First check if we have a systemInfo with a selectedVersion
    if (systemInfo?.selectedVersion != null) {
      return systemInfo!.selectedVersion!;
    }

    // If no version is selected in systemInfo, check the selectedVersions map
    if (!selectedVersions.containsKey(agentId) && agents != null) {
      final agentsList = agents!;
      if (agentsList.isNotEmpty) {
        for (final agent in agentsList) {
          if (agent.id == agentId && agent.version.isNotEmpty) {
            selectedVersions[agentId] = agent.version;
            return agent.version;
          }
        }
      }
    }

    return selectedVersions[agentId] ?? '';
  }

  // Method to update agent expanded state
  void updateAgentExpandedState(String agentId, bool expanded) {
    if (agents == null) return;

    for (int i = 0; i < agents!.length; i++) {
      final agent = agents![i];
      if (agent.id == agentId) {
        // Create a new agent with updated expanded state
        final updatedAgent = agent.copyWith(expanded: expanded);

        // Create a new list of agents with the updated agent
        final updatedAgents = List<AgentInfo>.from(agents!);
        updatedAgents[i] = updatedAgent;

        // Update the agents list
        agents = updatedAgents;
        return;
      }
    }
  }

  // Method to get agent by ID
  AgentInfo? getAgentById(String agentId) {
    if (agents == null) return null;

    for (final agent in agents!) {
      if (agent.id == agentId) {
        return agent;
      }
    }

    return null;
  }

  // Method to get section by ID
  AgentInfoSection? getSectionById(String sectionId) {
    if (agents == null) return null;

    final agentsList = agents!;
    for (final agent in agentsList) {
      for (final section in agent.sections) {
        if (section.id == sectionId) {
          return section;
        }
      }
    }

    return null;
  }

  factory AgentData.fromJson(Map<String, dynamic> json) => AgentData(
        agents: json["agents"] == null
            ? []
            : List<AgentInfo>.from(
                json["agents"]!.map((x) => AgentInfo.fromJson(x))),
        systemInfo: json["systemInfo"] == null
            ? null
            : AgentSystemInfo.fromJson(json["systemInfo"]),
        selectedVersions: json["selectedVersions"] == null
            ? {}
            : Map<String, String>.from(json["selectedVersions"]),
      );

  Map<String, dynamic> toJson() => {
        "agents": agents == null
            ? []
            : List<dynamic>.from(agents!.map((x) => x.toJson())),
        "systemInfo": systemInfo?.toJson(),
        "selectedVersions": selectedVersions,
      };
}

class AgentInfo {
  final String id;
  final String title;
  final String description;
  final String version;
  final String createdBy;
  final DateTime? createdDate;
  final String modifiedBy;
  final DateTime? modifiedDate;
  final List<AgentInfoSection> sections;
  bool? expanded;

  AgentInfo({
    required this.id,
    required this.title,
    this.description = '',
    this.version = '',
    this.createdBy = '',
    this.createdDate,
    this.modifiedBy = '',
    this.modifiedDate,
    this.sections = const [],
    this.expanded = false,
  });

  AgentInfo copyWith({
    String? id,
    String? title,
    String? description,
    String? version,
    String? createdBy,
    DateTime? createdDate,
    String? modifiedBy,
    DateTime? modifiedDate,
    List<AgentInfoSection>? sections,
    bool? expanded,
  }) =>
      AgentInfo(
        id: id ?? this.id,
        title: title ?? this.title,
        description: description ?? this.description,
        version: version ?? this.version,
        createdBy: createdBy ?? this.createdBy,
        createdDate: createdDate ?? this.createdDate,
        modifiedBy: modifiedBy ?? this.modifiedBy,
        modifiedDate: modifiedDate ?? this.modifiedDate,
        sections: sections ?? this.sections,
        expanded: expanded ?? this.expanded,
      );

  factory AgentInfo.fromJson(Map<String, dynamic> json) => AgentInfo(
        id: json["id"] ?? '',
        title: json["title"] ?? '',
        description: json["description"] ?? '',
        version: json["version"] ?? '',
        createdBy: json["createdBy"] ?? '',
        createdDate: json["createdDate"] == null
            ? null
            : DateTime.parse(json["createdDate"]),
        modifiedBy: json["modifiedBy"] ?? '',
        modifiedDate: json["modifiedDate"] == null
            ? null
            : DateTime.parse(json["modifiedDate"]),
        sections: json["sections"] == null
            ? []
            : List<AgentInfoSection>.from(
                json["sections"]!.map((x) => AgentInfoSection.fromJson(x))),
        expanded: json["expanded"] ?? false,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "description": description,
        "version": version,
        "createdBy": createdBy,
        "createdDate": createdDate?.toIso8601String(),
        "modifiedBy": modifiedBy,
        "modifiedDate": modifiedDate?.toIso8601String(),
        "sections": List<dynamic>.from(sections.map((x) => x.toJson())),
        "expanded": expanded,
      };
}

class AgentInfoSection {
  final String id;
  final String title;
  final String abbreviation;
  final List<String> items;
  final List<AgentInfoTab>? tabs;

  AgentInfoSection({
    required this.id,
    required this.title,
    this.abbreviation = '',
    this.items = const [],
    this.tabs,
  });

  AgentInfoSection copyWith({
    String? id,
    String? title,
    String? abbreviation,
    List<String>? items,
    List<AgentInfoTab>? tabs,
  }) =>
      AgentInfoSection(
        id: id ?? this.id,
        title: title ?? this.title,
        abbreviation: abbreviation ?? this.abbreviation,
        items: items ?? this.items,
        tabs: tabs ?? this.tabs,
      );

  factory AgentInfoSection.fromJson(Map<String, dynamic> json) =>
      AgentInfoSection(
        id: json["id"] ?? '',
        title: json["title"] ?? '',
        abbreviation: json["abbreviation"] ?? '',
        items: json["items"] == null
            ? []
            : List<String>.from(json["items"]!.map((x) => x)),
        tabs: json["tabs"] == null
            ? null
            : List<AgentInfoTab>.from(
                json["tabs"]!.map((x) => AgentInfoTab.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "abbreviation": abbreviation,
        "items": List<dynamic>.from(items.map((x) => x)),
        "tabs": tabs == null
            ? null
            : List<dynamic>.from(tabs!.map((x) => x.toJson())),
      };
}

class AgentInfoTab {
  final String? name;
  final bool? isSelected;
  final List<String>? data;

  AgentInfoTab({
    this.name,
    this.isSelected,
    this.data,
  });

  factory AgentInfoTab.fromJson(Map<String, dynamic> json) => AgentInfoTab(
        name: json["name"],
        isSelected: json["isSelected"],
        data: json["data"] == null
            ? []
            : List<String>.from(json["data"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "isSelected": isSelected,
        "data": data == null ? [] : List<dynamic>.from(data!.map((x) => x)),
      };
}

class AgentSystemInfo {
  final int? agentCount;
  final List<String>? bulletPoints;
  final String? headerText;
  final List<String?>? systemVersions;
  String? selectedVersion;

  AgentSystemInfo({
    this.agentCount,
    this.bulletPoints,
    this.headerText,
    this.systemVersions,
    this.selectedVersion,
  });

  // Create a copy with updated selectedVersion
  AgentSystemInfo copyWith({
    int? agentCount,
    List<String>? bulletPoints,
    String? headerText,
    List<String?>? systemVersions,
    String? selectedVersion,
  }) =>
      AgentSystemInfo(
        agentCount: agentCount ?? this.agentCount,
        bulletPoints: bulletPoints ?? this.bulletPoints,
        headerText: headerText ?? this.headerText,
        systemVersions: systemVersions ?? this.systemVersions,
        selectedVersion: selectedVersion ?? this.selectedVersion,
      );

  factory AgentSystemInfo.fromJson(Map<String, dynamic> json) =>
      AgentSystemInfo(
        agentCount: json["agentCount"],
        bulletPoints: json["bulletPoints"] == null
            ? []
            : List<String>.from(json["bulletPoints"]!.map((x) => x)),
        headerText: json["headerText"],
        systemVersions: json["systemVersions"] == null
            ? []
            : List<String?>.from(json["systemVersions"]!.map((x) => x)),
        selectedVersion: json["selectedVersion"],
      );

  Map<String, dynamic> toJson() => {
        "agentCount": agentCount,
        "bulletPoints": bulletPoints == null
            ? []
            : List<dynamic>.from(bulletPoints!.map((x) => x)),
        "headerText": headerText,
        "systemVersions": systemVersions == null
            ? []
            : List<dynamic>.from(systemVersions!.map((x) => x)),
        "selectedVersion": selectedVersion,
      };
}
