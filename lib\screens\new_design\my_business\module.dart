import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/models/custom_image.dart';
import 'package:nsl/models/books/chapter_model.dart';
import 'package:nsl/models/ui/collection_data.dart';
import 'package:nsl/providers/auth_provider.dart';
import 'package:nsl/providers/my_business_home_provider.dart';
import 'package:nsl/screens/new_design/my_business/collection.dart';
import 'package:nsl/screens/new_design/my_business/module_widgets.dart';
import 'package:nsl/screens/web_transaction/web_transaction_execution.dart';
import 'package:nsl/services/books_service.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/logger.dart';
import 'package:nsl/widgets/mobile/chat_input_field.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class CollectionModule extends StatefulWidget {
  final String? bookId;
  final String? bookName;
  CollectionModule({super.key, this.bookId, this.bookName});

  @override
  State<CollectionModule> createState() => _CollectionModuleState();
}

class _CollectionModuleState extends State<CollectionModule> {
  final TextEditingController chatController = TextEditingController();
  final FocusNode _chatFocusNode = FocusNode();
  bool showSingleRow = false;

  // Variables from web_collection_widgets.dart
  // Dynamic tree data from API using ChapterModel
  List<ChapterModel> chapters = [];

  // Loading and error states
  bool _isLoading = true;
  String? _errorMessage;

  // Books service for API calls
  final BooksService _booksService = BooksService();

  // Collection data for the back navigation - will be updated with actual book name
  final CollectionData _collectionData = CollectionData.loading();

  @override
  void initState() {
    super.initState();
    // Initialize the provider controllers
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<MyBusinessHomeProvider>().initializeControllers();
      _chatFocusNode.addListener(() {
        setState(() {
          showSingleRow = _chatFocusNode.hasFocus;
        });
      });
    });

    // Load chapter data
    _loadChapterData();
  }

  @override
  void dispose() {
    chatController.dispose();
    super.dispose();
  }

  /// Load chapter data from API
  Future<void> _loadChapterData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Get book ID from widget parameter or SharedPreferences
      String? bookId = widget.bookId;
      String? bookName;

      if (bookId == null) {
        final prefs = await SharedPreferences.getInstance();
        bookId = prefs.getString('selected_book_id');
        bookName = prefs.getString('selected_book_name');
      }

      if (bookId == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'No book ID provided';
        });
        return;
      }

      // Set book name immediately if available from SharedPreferences
      if (bookName != null) {
        setState(() {
          _collectionData.updateName(bookName!);
        });
      }

      Logger.info('Loading chapters for book: $bookId');

      final chapters = await _booksService.getBookChaptersWithUserTenant(
        bookId: bookId,
      );

      Logger.info('Loaded ${chapters.length} chapters');

      setState(() {
        this.chapters = chapters;
        _isLoading = false;

        // Update collection data with the book name from the first chapter (if not already set)
        if (chapters.isNotEmpty && chapters.first.bookName != null) {
          _collectionData.updateName(chapters.first.bookName!);
        } else if (_collectionData.isLoading) {
          _collectionData.updateName('Unknown Book');
        }
      });
    } catch (e) {
      Logger.error('Error loading chapter data: $e');
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load chapters: ${e.toString()}';

        // Set fallback book name if still loading
        if (_collectionData.isLoading) {
          _collectionData.updateName('Unknown Book');
        }
      });
    }
  }

  /// Load objectives for a specific chapter
  Future<void> _loadChapterObjectives(ChapterModel chapter) async {
    final chapterId = chapter.chapterId;
    if (chapterId == null) {
      Logger.error('No chapter ID found for loading objectives');
      return;
    }

    try {
      setState(() {
        chapter.isLoading = true;
        chapter.errorMessage = null;
      });

      Logger.info('Loading objectives for chapter: $chapterId');

      final objectives = await _booksService.getChapterObjectivesWithUserTenant(
        chapterId: chapterId,
      );

      Logger.info(
          'Loaded ${objectives.length} objectives for chapter: $chapterId');

      // Convert objectives to tree data format
      final List<Map<String, dynamic>> objectiveChildren =
          objectives.map((objective) {
        return {
          "name": objective.name ?? 'Unknown Objective',
          "isExpanded": false,
          "isSelected": false,
          "children": [],
          "image": "assets/images/my_business/box.svg",
          "dropdownImage": "assets/images/my_business/dropdown_collection.svg",
          "objectiveId": objective.goId,
          "chapterId": objective.chapterId,
          "bookId": objective.bookId,
          "type": "objective", // Mark as objective type
        };
      }).toList();

      setState(() {
        // Update the chapter with loaded objectives
        chapter.children = objectiveChildren;
        chapter.isLoading = false;
      });
    } catch (e) {
      Logger.error('Error loading objectives for chapter $chapterId: $e');
      setState(() {
        chapter.isLoading = false;
        chapter.errorMessage = 'Failed to load objectives: ${e.toString()}';
      });
    }
  }

  /// Handle objective selection - show WebTransactionExecution in bottom sheet
  // void _handleObjectiveSelection(String objectiveId, String objectiveName) {
  //   Logger.info('Objective selected: $objectiveId - $objectiveName');

  //   showModalBottomSheet(
  //     context: context,
  //     isScrollControlled: true,
  //     backgroundColor: Colors.transparent,
  //     builder: (context) => Container(
  //       height: MediaQuery.of(context).size.height * 0.9,
  //       decoration: BoxDecoration(
  //         color: Colors.white,
  //         borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
  //       ),
  //       child: Column(
  //         children: [
  //           // Header
  //           Container(
  //             padding: EdgeInsets.all(16),
  //             decoration: BoxDecoration(
  //               border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
  //             ),
  //             child: Row(
  //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //               children: [
  //                 Expanded(
  //                   child: Text(
  //                     objectiveName,
  //                     style: TextStyle(
  //                       fontSize: 18,
  //                       fontWeight: FontWeight.w600,
  //                     ),
  //                   ),
  //                 ),
  //                 IconButton(
  //                   onPressed: () => Navigator.pop(context),
  //                   icon: Icon(Icons.close),
  //                 ),
  //               ],
  //             ),
  //           ),
            
  //           // WebTransactionExecution content with one widget per row
  //           Expanded(
  //             child: WebTransactionExecution(
  //               objectiveId: objectiveId,
  //               objectiveName: objectiveName,
  //                isMobile:true,
  //             ),
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }
  void _handleObjectiveSelection(String objectiveId, String objectiveName) {
  Logger.info('Objective selected: $objectiveId - $objectiveName');

  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => ModuleWidgets(
        objectiveId: objectiveId,
        objectiveName: objectiveName,
        isMobile: true,
      ),
    ),
  );
}


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      
      resizeToAvoidBottomInset: true,
      backgroundColor: Color(0xffF7F9FB),
      body: Padding(
        padding: const EdgeInsets.only(left: 16.0,right:16,top:40),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    _buildBackNavigation(),
                  ],
                ),
                SizedBox(height: AppSpacing.sm),
                
                // Show loading indicator or chapter tree
                if (_isLoading)
                  Center(child: CircularProgressIndicator())
                else if (_errorMessage != null)
                  Container(
                    padding: EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Error loading chapters',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.red,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          _errorMessage!,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        SizedBox(height: 8),
                        GestureDetector(
                          onTap: _loadChapterData,
                          child: Text(
                            'Retry',
                            style: TextStyle(
                              fontSize: 14,
                              color: Color(0xff0058FF),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                else if (chapters.isEmpty)
                  Container(
                    padding: EdgeInsets.all(16),
                    child: Text(
                      'No chapters found',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  )
                else
                  // Display chapters and objectives using ExpansionTile
                  ListView.builder(
                    padding: EdgeInsets.all(0),
                    shrinkWrap: true,
                    itemCount: chapters.length,
                    itemBuilder: (context, index) {
                      final chapter = chapters[index];
                      return 
                       ChapterExpansionTile(
                        chapter: chapter,
                        onObjectiveSelected: _handleObjectiveSelection,
                        onLoadObjectives: _loadChapterObjectives,
                      );
                    },
                  ),
              ],
            ),

            // Chat field at the bottom
            Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: ChatInputField(
                focusNode: _chatFocusNode,
                chatController: chatController,
                sendMessage: () {},
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build back navigation element with arrow and text
  Widget _buildBackNavigation() {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => MyBusinessCollection(),
            ),
          );
        },
        child: Container(
          padding: EdgeInsets.only(
            top: AppSpacing.lg,
            bottom: AppSpacing.xxs,
          ),
          child: CustomImage.asset(
            'assets/images/my_business/back_arrow.svg',
            width: 12,
            height: 12,
            color: Colors.black,
          ).toWidget(),
        ),
      ),
    );
  }
}

class ChapterExpansionTile extends StatefulWidget {
  final ChapterModel chapter;
  final Function(String, String) onObjectiveSelected;
  final Function(ChapterModel) onLoadObjectives;

  const ChapterExpansionTile({
    Key? key,
    required this.chapter,
    required this.onObjectiveSelected,
    required this.onLoadObjectives,
  }) : super(key: key);

  @override
  State<ChapterExpansionTile> createState() => _ChapterExpansionTileState();
}

class _ChapterExpansionTileState extends State<ChapterExpansionTile> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSpacing.xs),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade300,
            blurRadius: 2,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child:
      
       ExpansionTile(
        iconColor: Colors.black,
        initiallyExpanded: _isExpanded,shape: Border.all(color: Colors.transparent),
        onExpansionChanged: (expanded) async {
          setState(() {
            _isExpanded = expanded;
          });
          
          // Load objectives when expanded if not already loaded
          if (expanded && widget.chapter.children.isEmpty && !widget.chapter.isLoading) {
            await widget.onLoadObjectives(widget.chapter);
          }
        },
        leading: SvgPicture.asset(
          'assets/images/my_business/box_add.svg',
          width: 16,
          height: 16,
        
        ),
        title: Text(
          widget.chapter.chapterName ?? 'Unknown Chapter',
          style: const TextStyle(
            fontSize: 14,
            color: Colors.black,
            fontWeight: FontWeight.w500,
          ),
        ),
        children: [
          // Show loading, error, or objectives
          if (widget.chapter.isLoading)
            Container(
              padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              child: Center(
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            )
          else if (widget.chapter.errorMessage != null)
            Container(
              padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              child: Column(
                children: [
                  Text(
                    'Error loading objectives',
                    style: TextStyle(color: Colors.red, fontSize: 12),
                  ),
                  SizedBox(height: 4),
                  GestureDetector(
                    onTap: () => widget.onLoadObjectives(widget.chapter),
                    child: Text(
                      'Retry',
                      style: TextStyle(
                        color: Color(0xff0058FF),
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            )
          else if (widget.chapter.children.isEmpty)
            Container(
              padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              child: Text(
                'No objectives found',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            )
          else
            // Display objectives as children
            ...widget.chapter.children.map((objective) => ObjectiveTile(
              objective: objective,
              onTap: () {
                final objectiveId = objective['objectiveId'] as String?;
                final objectiveName = objective['name'] as String?;
                
                if (objectiveId != null && objectiveName != null) {
                  widget.onObjectiveSelected(objectiveId, objectiveName);
                }
              },
            )).toList(),
        ],
      ),
    );
  }
}

class ObjectiveTile extends StatelessWidget {
  final Map<String, dynamic> objective;
  final VoidCallback? onTap;

  const ObjectiveTile({
    Key? key,
    required this.objective,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(left: 16, right: 16, top: 2, bottom: 2),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        // decoration: BoxDecoration(
        //   // color: Colors.grey[50],
        //   borderRadius: BorderRadius.circular(AppSpacing.xs),
        //   // border: Border.all(color: Colors.grey.shade200),
        // ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Objective icon
            CustomImage.asset(
              'assets/images/my_business/box.svg',
              width: 14,
              height: 14,
            ).toWidget(),
            const SizedBox(width: AppSpacing.lg),
            Expanded(
              child: Text(
                objective['name'] ?? 'Unknown Objective',
                style: const TextStyle(
                  fontSize: 13,
                  color: Colors.black,
                ),
              ),
            ),
            // Arrow icon to indicate it's clickable
            // Icon(
            //   Icons.arrow_forward_ios,
            //   size: 12,
            //   color: Colors.black,
            // ),
          ],
        ),
      ),
    );
  }
}

class FeatureTile extends StatelessWidget {
  final String imagePath; // Left icon image path
  final String label;
  final VoidCallback? onTap;
  final bool isLoading;

  const FeatureTile({
    Key? key,
    required this.imagePath,
    required this.label,
    this.onTap,
    this.isLoading = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: isLoading ? null : onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppSpacing.xs),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade300,
              blurRadius: 2,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset(imagePath, width: 16, height: 16),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                label,
                style: const TextStyle(fontSize: 14),
              ),
            ),
            if (isLoading)
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xff0058FF)),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
