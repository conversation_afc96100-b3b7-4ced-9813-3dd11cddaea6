import 'package:flutter/material.dart';

/// A reusable resizable panel widget that can be resized horizontally
/// Used for side panels, tree navigation, and other resizable UI components
class ResizablePanel extends StatefulWidget {
  /// The child widget to display inside the panel
  final Widget child;
  
  /// Current width of the panel
  final double width;
  
  /// Minimum allowed width
  final double minWidth;
  
  /// Maximum allowed width
  final double maxWidth;
  
  /// Callback when the panel is resized
  final Function(double) onResize;
  
  /// Position of the resize handle (left or right side)
  final ResizeHandlePosition handlePosition;
  
  /// Color of the resize handle when active
  final Color? activeHandleColor;
  
  /// Color of the resize handle when inactive
  final Color? inactiveHandleColor;
  
  /// Width of the resize handle area
  final double handleWidth;
  
  /// Height of the resize handle indicator
  final double handleIndicatorHeight;
  
  /// Whether to show the resize handle indicator
  final bool showHandleIndicator;

  const ResizablePanel({
    super.key,
    required this.child,
    required this.width,
    required this.minWidth,
    required this.maxWidth,
    required this.onResize,
    this.handlePosition = ResizeHandlePosition.left,
    this.activeHandleColor,
    this.inactiveHandleColor,
    this.handleWidth = 12.0,
    this.handleIndicatorHeight = 20.0,
    this.showHandleIndicator = true,
  });

  @override
  State<ResizablePanel> createState() => _ResizablePanelState();
}

class _ResizablePanelState extends State<ResizablePanel> {
  bool isResizing = false;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Main content
        SizedBox(
          width: widget.width,
          child: widget.child,
        ),

        // Resize handle
        Positioned(
          left: widget.handlePosition == ResizeHandlePosition.left ? 0 : null,
          right: widget.handlePosition == ResizeHandlePosition.right ? 0 : null,
          top: 0,
          bottom: 0,
          child: GestureDetector(
            onHorizontalDragStart: (_) {
              setState(() {
                isResizing = true;
              });
            },
            onHorizontalDragUpdate: (details) {
              double newWidth;
              
              // Calculate new width based on handle position
              if (widget.handlePosition == ResizeHandlePosition.left) {
                // For left handle, subtract drag delta
                newWidth = widget.width - details.delta.dx;
              } else {
                // For right handle, add drag delta
                newWidth = widget.width + details.delta.dx;
              }
              
              // Ensure width stays within bounds
              if (newWidth >= widget.minWidth && newWidth <= widget.maxWidth) {
                widget.onResize(newWidth);
              }
            },
            onHorizontalDragEnd: (_) {
              setState(() {
                isResizing = false;
              });
            },
            child: MouseRegion(
              cursor: SystemMouseCursors.resizeLeftRight,
              onEnter: (_) {
                setState(() {
                  isResizing = true;
                });
              },
              onExit: (_) {
                setState(() {
                  isResizing = false;
                });
              },
              child: Container(
                width: widget.handleWidth,
                color: Colors.transparent,
                child: widget.showHandleIndicator
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              width: 4,
                              height: widget.handleIndicatorHeight,
                              margin: EdgeInsets.symmetric(vertical: 4),
                              decoration: BoxDecoration(
                                color: isResizing
                                    ? (widget.activeHandleColor ?? Colors.blue.shade700)
                                    : (widget.inactiveHandleColor ?? Colors.grey.withValues(alpha: 128)),
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                          ],
                        ),
                      )
                    : null,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// Enum to specify the position of the resize handle
enum ResizeHandlePosition {
  /// Handle on the left side (drag left to shrink, right to expand)
  left,
  /// Handle on the right side (drag right to expand, left to shrink)
  right,
}
