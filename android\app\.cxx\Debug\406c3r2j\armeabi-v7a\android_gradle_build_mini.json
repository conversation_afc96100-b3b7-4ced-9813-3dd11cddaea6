{"buildFiles": ["C:\\Users\\<USER>\\Downloads\\flutter_windows_3.27.0-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\OneDrive - Brane Enterprises Pvt Limited\\Desktop\\design-time-ui-flutter\\android\\app\\.cxx\\Debug\\406c3r2j\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\OneDrive - Brane Enterprises Pvt Limited\\Desktop\\design-time-ui-flutter\\android\\app\\.cxx\\Debug\\406c3r2j\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}